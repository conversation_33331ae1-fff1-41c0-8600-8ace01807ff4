Host vshp
  Hostname ************* 
  User bg0krpnv
  SetEnv CECC_AX_EMC_PSU_IP=************** CECC_AX_EMC_PSU_CH=1 CECC_AX_EMC_IP=*************** CECC_AX_EOL_PSU_IP=************** CECC_AX_EOL_PSU_CH=1 CECC_AX_EOL_IP=*************** CECC_AX_4GB_PSU_IP=************** CECC_AX_4GB_PSU_CH=2 CECC_AX_4GB_IP=*************** CECC_AX_32GB_PSU_IP=************** CECC_AX_32GB_PSU_CH=2 CECC_AX_32GB_IP=*************** CECC_AX_DEV_PSU_IP=************** CECC_AX_DEV_PSU_CH=3 CECC_AX_DEV_IP=*************** CECC_AX_TEST_PSU_IP=************** CECC_AX_TEST_PSU_CH=2 CECC_AX_TEST_IP=*************** CECC_AX_DEF_IP=************
Host bochko
  Hostname **************
  User bg0krpnv
  SetEnv CECC_AX_EMC_PSU_IP=************** CECC_AX_EMC_PSU_CH=1 CECC_AX_EMC_IP=*************** CECC_AX_EOL_PSU_IP=************** CECC_AX_EOL_PSU_CH=1 CECC_AX_EOL_IP=*************** CECC_AX_4GB_PSU_IP=************** CECC_AX_4GB_PSU_CH=2 CECC_AX_4GB_IP=*************** CECC_AX_32GB_PSU_IP=************** CECC_AX_32GB_PSU_CH=2 CECC_AX_32GB_IP=*************** CECC_AX_DEV_PSU_IP=************** CECC_AX_DEV_PSU_CH=3 CECC_AX_DEV_IP=*************** CECC_AX_TEST_PSU_IP=************** CECC_AX_TEST_PSU_CH=2 CECC_AX_TEST_IP=*************** CECC_AX_DEF_IP=************
Host CECC_AX_EMC
  Hostname ***************
  User root
  StrictHostKeyChecking no
  UserKnownHostsFile /dev/null
Host CECC_AX_EOL
  Hostname ***************
  User root
  StrictHostKeyChecking no
  UserKnownHostsFile /dev/null
Host CECC_AX_4GB
  Hostname ***************
  User root
  StrictHostKeyChecking no
  UserKnownHostsFile /dev/null
Host CECC_AX_32GB
  Hostname ***************
  User root
  StrictHostKeyChecking no
  UserKnownHostsFile /dev/null
Host CECC_AX_DEV
  Hostname ***************
  User root
  StrictHostKeyChecking no
  UserKnownHostsFile /dev/null
Host CECC_AX_TEST
  Hostname ***************
  User root
  StrictHostKeyChecking no
  UserKnownHostsFile /dev/null
Host CECC_AX_DEFAULT
  Hostname ************
  User root
  StrictHostKeyChecking no
  UserKnownHostsFile /dev/null

