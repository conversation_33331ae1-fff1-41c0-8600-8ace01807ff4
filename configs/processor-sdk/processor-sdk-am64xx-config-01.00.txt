# This file takes repo entries in the format
# repo name,repo uri,repo branch,repo commit[,layers=layer1:layer2...:layern]
poky,https://gitlab.festo.company/festo/yocto/third-party/poky.git,scarthgap,2034fc38eb4e63984d9bd6b260aa1bf95ce562e4,layers=meta:meta-poky:meta-yocto-bsp
meta-openembedded,https://gitlab.festo.company/festo/yocto/third-party/meta-openembedded.git,scarthgap,72018ca1b1a471226917e8246e8bbf9a374ccf97,layers=meta-networking:meta-python:meta-oe:meta-gnome:meta-filesystems:meta-multimedia:meta-webserver
bitbake,https://gitlab.festo.company/festo/yocto/third-party/bitbake.git,2.8,8714a02e13477a9d97858b3642e05f28247454b5
meta-ti,https://git.yoctoproject.org/meta-ti,scarthgap,7394a1b438eaaddd4e74d354e134f9dc48d489e2,layers=meta-ti-extras:meta-ti-bsp
meta-arm,https://gitlab.festo.company/festo/yocto/third-party/meta-arm.git,scarthgap,58268ddccbe2780de28513273e15193ee949987b,layers=meta-arm:meta-arm-toolchain
meta-security,https://gitlab.festo.company/festo/yocto/third-party/meta-security.git,scarthgap,459d837338ca230254baa2994f870bf6eb9d0139,layers=meta-tpm
meta-rauc,https://gitlab.festo.company/festo/yocto/third-party/meta-rauc.git,scarthgap,1e3e6b334defd7fbf95cb43d23975e7b3de4b520,layers=
meta-virtualization,https://gitlab.festo.company/festo/yocto/third-party/meta-virtualization.git,scarthgap,6f3c1d8f90947408a6587be222fec575a1ca5195,layers=
meta-clang,https://gitlab.festo.company/festo/yocto/third-party/meta-clang.git,scarthgap,2b7433611d80f6d0ee1b04156fa91fc73d3c2665,layers=
meta-hardware,https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-hardware.git,cepe-v0.0.30,0b3081f9e5d2a143deda1959d03e67ed66501d3e,layers=
meta-pxc,https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-pxc.git,cepe-v0.0.30,4b69ce42f85de22690ff885707649d2aa3db3fff,layers=
meta-arp,https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-arp,cepe-v0.0.30,92992cf57c806158460982cccc252b046fbbf2e3,layers=
meta-festo,https://gitlab.festo.company/festo/yocto/ea/controls/meta/meta-festo.git,main,HEAD,layers=
meta-festo-midrange,https://gitlab.festo.company/festo/yocto/ea/controls/p30/meta-festo-midrange.git,main,HEAD,layers=
OECORELAYERCONF=./sample-files/bblayers.conf.sample
OECORELOCALCONF=./../boards/am64xx/local.conf
BITBAKE_INCLUSIVE_VARS=yes
