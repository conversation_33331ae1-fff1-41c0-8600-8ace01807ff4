# This file takes repo entries in the format
# repo name,repo uri,repo branch,repo commit[,layers=layer1:layer2...:layern]

bitbake,https://gitlab.festo.company/festo/yocto/third-party/bitbake.git,2.0,0c6f86b60cfba67c20733516957c0a654eb2b44c
meta-edgeai,https://gitlab.festo.company/festo/yocto/third-party/meta-edgeai.git,kirkstone,a1d9091cd914ce0bc69958891628a4cf86ed3e96,layers=
meta-processor-sdk,https://gitlab.festo.company/festo/yocto/third-party/meta-processor-sdk.git,kirkstone,9dad5765d33783f940ba3c32f4fc431e2b308621,layers=
meta-arago,https://gitlab.festo.company/festo/yocto/third-party/meta-arago.git,kirkstone,09.00.00.006,layers=meta-arago-distro:meta-arago-extras:meta-arago-demos
meta-qt5,https://gitlab.festo.company/festo/yocto/third-party/meta-qt5.git,kirkstone,bff5bd937f0776166e81a63f3dd39ede348ef758,layers=
meta-virtualization,https://gitlab.festo.company/festo/yocto/third-party/meta-virtualization.git,kirkstone,b3b3dbc67504e8cd498d6db202ddcf5a9dd26a9d,layers=
meta-openembedded,https://gitlab.festo.company/festo/yocto/third-party/meta-openembedded.git,kirkstone,346753705e49a2486867dc150181a1c7f4d69377,layers=meta-networking:meta-python:meta-oe:meta-gnome:meta-filesystems:meta-multimedia
meta-ti,https://gitlab.festo.company/festo/yocto/third-party/meta-ti.git,kirkstone,09.00.00.006,layers=meta-ti-extras:meta-ti-bsp
meta-arm,https://gitlab.festo.company/festo/yocto/third-party/meta-arm.git,kirkstone,96aad3b29aa7a5ee4df5cf617a6336e5218fa9bd,layers=meta-arm:meta-arm-toolchain
oe-core,https://gitlab.festo.company/festo/yocto/third-party/openembedded-core.git,kirkstone,f20a12ead2d5890e88e7f4ce149a777de47edc48,layers=meta
OECORELAYERCONF=./sample-files/bblayers.conf.sample
OECORELOCALCONF=./sample-files/local-arago64-v2.conf.sample
BITBAKE_INCLUSIVE_VARS=yes
