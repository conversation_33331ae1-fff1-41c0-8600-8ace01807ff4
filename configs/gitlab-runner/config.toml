concurrent = 1
check_interval = 0
shutdown_timeout = 0

[session_server]
  session_timeout = 1800

[[runners]]
  name = "nat-deb9-in-docker-17.2.1-exec-docker"
  url = "https://gitlab.festo.company"
  id = 3397
  token = "glrt-cxK5HsL5JTBZqzAJkxjD"
  token_obtained_at = 2024-09-16T12:40:41Z
  token_expires_at = 0001-01-01T00:00:00Z
  executor = "docker"
  environment = ["FF_NETWORK_PER_BUILD=1"]
  [runners.custom_build_dir]
  [runners.cache]
    MaxUploadedArchiveSize = 0
    [runners.cache.s3]
    [runners.cache.gcs]
    [runners.cache.azure]
  [runners.docker]
    cpuset_cpus = "0,1,2,3,4,5"
    cpus = "6"
    tls_verify = false
    image = "gitlab.festo.company:5050/festo/yocto/ea/controls/infrastructure/crops_poky_container/ubuntu-22.04/crops_poky_container:9b7d5fd7"
    user = "usersetup"
    pull_policy = "if-not-present"
    privileged = true
    disable_entrypoint_overwrite = false
    oom_kill_disable = false
    disable_cache = false
    volumes = ["/mnt/ext/yocto/docker/volumes/builds/midrange:/builds",
               "/mnt/ext/yocto/docker/volumes/builds/downloads:/builds/downloads",
               "/mnt/ext/yocto/docker/volumes/builds/testdownloads:/builds/testdownloads",
               "/mnt/ext/yocto/docker/volumes/builds/sstate-cache:/builds/sstate-cache",
               "/mnt/ext/yocto/docker/volumes/builds/testsstate-cache:/builds/testsstate-cache",
               "/cache"]
    shm_size = 0
    network_mtu = 1402
  [runners.docker.ulimit]
    "nofile" = "10000"
