#!/bin/bash

set -e

config_file=$1

# Set default config file if not provided
if [[ -z "$config_file" ]]; then
  echo "[WARRNING] Please specify a config file for next processing!"
  echo "Available config files in configs/processor-sdk are:"
  #ls -al configs/processor-sdk/*.txt
  for file in configs/processor-sdk/*.txt; do
    echo "$(basename $file)"
  done
  exit 0
fi

# Check if the provided config file exists
if [[ ! -f "configs/processor-sdk/$config_file" ]]; then
  echo "Config file 'configs/processor-sdk/$config_file' not found!"
  exit 1
fi

# Run the Festo sources
if [[ -z "$config_file" ]]; then
  if [[ ! -d "tisdk" ]]; then
    git clone https://gitlab.festo.company/festo/yocto/third-party/tisdk.git
  fi
  cd tisdk
  ./oe-layertool-setup.sh -f "../configs/processor-sdk/$config_file"
else
  if [[ ! -d "tisdk" ]]; then
    git clone https://git.ti.com/git/arago-project/oe-layersetup.git tisdk
  fi
  cd tisdk
  ./oe-layertool-setup.sh -f "../configs/processor-sdk/$config_file"
fi
