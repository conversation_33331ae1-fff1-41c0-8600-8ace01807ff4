if [[ $# -eq 0 ]]
then
  echo 'Usage: ./usbflasher_remote.sh <path-to-usbflasher-image> <remote target> <remote PC>'
  echo 'Targets:CECC_AX_EMC, CECC_AX_EOL, CECC_AX_4GB, CECC_AX_32GB, CECC_AX_DEV'
  echo 'PC: bochko, vshp'
  exit 0
fi
echo '#################'
echo 'Power is applied!'
echo '#################'
ssh $3 "psuctl.py --ip \$$2_PSU_IP --channel \$$2_PSU_CH -v 24 -i 100 -o 1"
echo '##############################'
echo 'Uploading usb flasher image...'
echo '##############################'
scp -J $3 $1 $2:/root/.
sleep 1
echo '##############################'
echo 'Flashing image to USB'
echo '##############################'
ssh -J $3 $2 'bmaptool copy --nobmap flasher* /dev/sda; sync'
sleep 3
echo '##############################'
echo 'Deleting eMMC boot partition'
echo '##############################'
ssh -J $3 $2 'echo 0 > /sys/block/mmcblk0boot0/force_ro ; sudo dd if=/dev/zero of=/dev/mmcblk0boot0 bs=512 count=1'
sleep 1
echo '##############################'
echo 'Reboot and wait 250 sec for USB flash to target...'
echo '##############################'
echo 'Power is off!'
ssh $3 "psuctl.py --ip \$$2_PSU_IP --channel \$$2_PSU_CH -v 24 -i 100 -o 0"
sleep 1
echo 'Power is on!'
ssh $3 "psuctl.py --ip \$$2_PSU_IP --channel \$$2_PSU_CH -v 24 -i 100 -o 1"
sleep 250
echo 'Power is off!'
ssh $3 "psuctl.py --ip \$$2_PSU_IP --channel \$$2_PSU_CH -v 24 -i 100 -o 0"
sleep 1
echo 'Power is on!'
ssh $3 "psuctl.py --ip \$$2_PSU_IP --channel \$$2_PSU_CH -v 24 -i 100 -o 1"
echo '##############################'
echo 'Waiting for availability...'
echo '##############################'
sleep 10
while ! ssh $3 "ping -c1 \$CECC_AX_DEF_IP >/dev/null"
  do echo "Ping Fail"
done
sleep 1
echo '##############################'
echo "Reflash is ready... Changing IP, write local key and reboot"
echo '##############################'
#ssh vshp "echo 'root' > /dev/ttyACM0"
#sleep 1
#ssh vshp "echo 'root' > /dev/ttyACM0"
#sleep 2
#ssh vshp "echo 'ifconfig ETH ************** netmask *************' > /dev/ttyACM0"
REMOTE=$(ssh -G $2 | awk '/^hostname / { print $2 }')
DEF=$(ssh -G CECC_AX_DEFAULT | awk '/^hostname / { print $2 }')
sleep 20
cat ~/.ssh/id_rsa.pub | ssh -J $3 CECC_AX_DEFAULT "cat >> .ssh/authorized_keys && sed -i -e 's/$DEF/$REMOTE/' -e 's/Gateway=.*/Gateway=*************/' /etc/systemd/network/79-if-1.network && reboot"
sleep 1
echo '##############################'
echo 'Waiting for availability...'
echo '##############################'
sleep 10
while ! ssh $3 "ping -c1 \$$2_IP >/dev/null"
  do echo "Ping Fail"
done
echo '##############################'
echo " SSHing into $2"
echo '##############################'
sleep 1
ssh -J $3 $2
