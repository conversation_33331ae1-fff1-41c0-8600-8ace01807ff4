if [[ $# -eq 0 ]]
then
    echo 'Usage: ./rauc_remote.sh <path-to-rauc-image> <remote target> <remote PC>'
    echo 'Targets:CECC_AX_EMC, CECC_AX_EOL, CECC_AX_4GB, CECC_AX_32GB, CECC_AX_DEV'
    echo 'PC: bochko, vshp'
    exit 0
fi
echo '###################'
echo 'Power is applied!'
echo '###################'
ssh $3 "psuctl.py --ip \$$2_PSU_IP --channel \$$2_PSU_CH -v 24 -i 100 -o 1"
sleep 1
echo '##########################'
echo 'RAUC image is uploading...'
echo '##########################'
scp -J $3 $1 $2:/root/.
sleep 2
echo '###########################'
echo 'RAUC image is installing...'
echo '###########################'
ssh -J $3 $2 'rauc install *'
sleep 2
echo '###################'
echo 'Target reboot'
echo '###################'
ssh -J $3 $2 'reboot'
sleep 10
echo '##################################'
echo 'Waiting for target availability...'
echo '##################################'
while ! ssh $3 "ping -c1 \$$2_IP >/dev/null"
  do echo "Ping Fail"
done
echo '###########################################'
echo 'Type root to add local key to target...'
echo '###########################################'
cat ~/.ssh/id_rsa.pub | ssh -J $3 $2 'cat >> .ssh/authorized_keys'
echo '######################################'
echo "Reflash is ready... Now SSHing into $2"
echo '######################################'
sleep 1
ssh -J $3 $2
