510101 15:29:04.354625 --- Starting bitbake server pid 510101 at 2025-07-30 15:29:04.354591 ---
510101 15:29:04.355600 Started bitbake server pid 510101
510101 15:29:04.355883 Entering server connection loop
510101 15:29:04.355907 Lockfile is: /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/bitbake.lock
Socket is /home/<USER>/Midlayer/p30_gitlab_midrange/midrange/bitbake.sock (True)
510101 15:29:04.357090 Accepting [<socket.socket fd=6, family=AddressFamily.AF_UNIX, type=SocketKind.SOCK_STREAM, proto=0, laddr=bitbake.sock>] ([])
510101 15:29:04.357131 Processing Client
510101 15:29:04.357156 Connecting Client
510101 15:29:04.357438 Running command ['setFeatures', [2, 1]]
510101 15:29:04.357479 Sending reply (None, None)
510101 15:29:04.357546 Command Completed (socket: True)
510101 15:29:04.358192 Running command ['updateConfig', {'halt': True, 'force': False, 'invalidate_stamp': None, 'dry_run': False, 'dump_signatures': [], 'extra_assume_provided': [], 'profile': False, 'prefile': [], 'postfile': [], 'server_timeout': None, 'nosetscene': False, 'setsceneonly': False, 'skipsetscene': False, 'runall': [], 'runonly': None, 'writeeventlog': None, 'build_verbose_shell': False, 'build_verbose_stdout': False, 'default_loglevel': 20, 'debug_domains': {}}, {'SHELL': '/bin/bash', 'BB_ENV_PASSTHROUGH_ADDITIONS': 'MACHINE DISTRO TCMODE TCLIBC http_proxy ftp_proxy https_proxy all_proxy ALL_PROXY no_proxy SSH_AGENT_PID SSH_AUTH_SOCK BB_SRCREV_POLICY SDKMACHINE BB_NUMBER_THREADS PARALLEL_MAKE GIT_PROXY_COMMAND GIT_PROXY_IGNORE SOCKS5_PASSWD SOCKS5_USER OEBASE META_SDK_PATH TOOLCHAIN_TYPE TOOLCHAIN_BRAND TOOLCHAIN_BASE TOOLCHAIN_PATH TOOLCHAIN_PATH_ARMV5 TOOLCHAIN_PATH_ARMV7 TOOLCHAIN_PATH_ARMV8 EXTRA_TISDK_FILES TISDK_VERSION ARAGO_BRAND ARAGO_RT_ENABLE ARAGO_SYSTEST_ENABLE ARAGO_KERNEL_SUFFIX TI_SECURE_DEV_PKG_CAT TI_SECURE_DEV_PKG_AUTO TI_SECURE_DEV_PKG_K3 ARAGO_SYSVINIT SYSFW_FILE', 'PWD': '/home/<USER>/Midlayer/p30_gitlab_midrange/midrange', 'LOGNAME': 'sergey', 'OEBASE': '/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk', 'HOME': '/home/<USER>', 'USER': 'sergey', 'PATH': '/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/meta-rauc/scripts:/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/meta-arm/scripts:/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/poky/scripts:/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/meta-security/scripts:/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/meta-virtualization/scripts:/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/bin:/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/poky/bitbake/bin:/usr/bin:/usr/local/bin:/home/<USER>/.vscode-server/cli/servers/Stable-4949701c880d4bdb949e3c0e6b400288da7f474b/server/bin/remote-cli:/home/<USER>/.local/bin:/usr/bin:/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'LC_ALL': 'en_US.UTF-8', 'COLORTERM': 'truecolor', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0/.noConfigDebugAdapterEndpoints/endpoint-d6944146ca2aa020.txt', 'TERM_PROGRAM_VERSION': '1.99.2', 'LC_ADDRESS': 'bg_BG.UTF-8', 'LC_NAME': 'bg_BG.UTF-8', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'LC_MONETARY': 'bg_BG.UTF-8', 'XDG_SESSION_TYPE': 'tty', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0/bundled/libs/debugpy', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-4949701c880d4bdb949e3c0e6b400288da7f474b/server/node', 'MOTD_SHOWN': 'pam', 'LC_PAPER': 'bg_BG.UTF-8', 'LANG': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-4949701c880d4bdb949e3c0e6b400288da7f474b/server/extensions/git/dist/askpass.sh', 'SSH_CONNECTION': '************ 49579 ************** 22', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'LC_IDENTIFICATION': 'bg_BG.UTF-8', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-f2fe434493.sock', 'SHLVL': '1', 'LC_TELEPHONE': 'bg_BG.UTF-8', 'LC_MEASUREMENT': 'bg_BG.UTF-8', 'XDG_SESSION_ID': '1937', 'XDG_RUNTIME_DIR': '/run/user/1000', 'SSL_CERT_FILE': '/usr/lib/ssl/certs/ca-certificates.crt', 'SSH_CLIENT': '************ 49579 22', 'LC_TIME': 'bg_BG.UTF-8', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-4949701c880d4bdb949e3c0e6b400288da7f474b/server/extensions/git/dist/askpass-main.js', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-4949701c880d4bdb949e3c0e6b400288da7f474b/server/bin/helpers/browser.sh', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'BUILDDIR': '/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/build', 'LC_NUMERIC': 'bg_BG.UTF-8', 'OLDPWD': '/home/<USER>/Midlayer/p30_gitlab_midrange/midrange', 'TERM_PROGRAM': 'vscode', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-25b8095f-c5ef-4f7f-8eac-b8ebfa36114b.sock', '_': '/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/bin/bitbake'}, ['/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/bin/bitbake', '-c', 'cleansstate', 'arp']]
510101 15:29:04.361680 Sending reply (None, 'bb.BBHandledException()\nTraceback (most recent call last):\n  File "/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/lib/bb/command.py", line 90, in runCommand\n    result = command_method(self, commandline)\n  File "/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/lib/bb/command.py", line 290, in updateConfig\n    command.cooker.updateConfigOpts(options, environment, cmdline)\n  File "/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/lib/bb/cooker.py", line 462, in updateConfigOpts\n    self.reset()\n  File "/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/lib/bb/cooker.py", line 1735, in reset\n    self.initConfigurationData()\n  File "/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/lib/bb/cooker.py", line 282, in initConfigurationData\n    self.databuilder.parseBaseConfiguration()\n  File "/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/lib/bb/cookerdata.py", line 261, in parseBaseConfiguration\n    self.data = self.parseConfigurationFiles(self.prefiles, self.postfiles)\n  File "/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/lib/bb/cookerdata.py", line 463, in parseConfigurationFiles\n    bb.fatal(msg)\n  File "/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/tisdk/sources/bitbake/lib/bb/__init__.py", line 190, in fatal\n    raise BBHandledException()\nbb.BBHandledException\n')
510101 15:29:04.361916 Command Completed (socket: True)
510101 15:29:04.610923 Processing Client
510101 15:29:04.610998 Disconnecting Client (socket: True)
510101 15:29:04.611118 No timeout, exiting.
510101 15:29:04.711343 Exiting (socket: True)
510101 15:29:04.711992 Exiting as we could obtain the lock
sys:1: ResourceWarning: unclosed file <_io.TextIOWrapper name='/home/<USER>/Midlayer/p30_gitlab_midrange/midrange/bitbake-cookerdaemon.log' mode='a+' encoding='UTF-8'>
